/**
 * Chứ<PERSON> năng: <PERSON><PERSON><PERSON> thị bảng danh sách, form tìm kiếm và xử lý các tương tác người dùng
 */

import React, {memo, useCallback, useMemo, useRef, useState, useContext} from "react";
import {Col, Form, Table, TableColumnType, Tag, Row} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";

import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import CauHinhMauHopDongContext from "./index.context";
import {
  FormTimKiemCauHinhMauHopDong, 
  tableC<PERSON>umn<PERSON>onfigs, 
  TableCauHinhMauHopDongColumnDataType,
  createSearchDropdownOptions
} from "./index.configs";
import {ModalChiTietCauHinhMauHopDong, IModalChiTietCauHinhMauHopDongRef} from "./Component";

type DataIndex = keyof TableCauHinhMauHopDongColumnDataType;

// DESTRUCTURING FORM SEARCH VALUES 
const {
  ma_doi_tac_ql,
  nv,
  ma_sp,
  ten,
  trang_thai,
} = FormTimKiemCauHinhMauHopDong;

/**
 * Component này chứa table danh sách cấu hình mẫu hợp đồng, form tìm kiếm, pagination và modal chi tiết
 */
const CauHinhMauHopDongContent: React.FC = memo(() => {
  // CONTEXT DATA 
  const {
    danhSachCauHinhMauHopDong,
    listSanPham,
    loading,
    tongSoDong,
    getChiTietCauHinhMauHopDong,
    searchCauHinhMauHopDong,
    filterParams,
    setFilterParams,
  } = useContext(CauHinhMauHopDongContext);

  // STATE MANAGEMENT 
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">(""); 
  const searchInput = useRef<HTMLInputElement>(null); 
  const modalChiTietRef = useRef<IModalChiTietCauHinhMauHopDongRef>(null);

  // SEARCH FORM
  const [searchForm] = Form.useForm();
  const searchDoiTac = Form.useWatch('ma_doi_tac_ql', searchForm);
  const searchNghiepVu = Form.useWatch('nv', searchForm);

  // DROPDOWN OPTIONS - USING CENTRALIZED FUNCTIONS 
  const dropdownOptions = useMemo(() => 
    createSearchDropdownOptions(listSanPham, searchDoiTac, searchNghiepVu),
    [listSanPham, searchDoiTac, searchNghiepVu]
  );

  // TABLE DATA WITH STT 
  const dataTableWithSTT = useMemo(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;
      
      const tableData =
        danhSachCauHinhMauHopDong?.map((item, index) => ({
          ...item,
          key: item.bt ? `${item.bt}` : `row-${index}`,
          sott: (currentPage - 1) * currentPageSize + index + 1,
        })) || [];

      const arrEmptyRow = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.error("dataTableWithSTT error", error);
      return [];
    }
  }, [danhSachCauHinhMauHopDong, filterParams?.trang, filterParams?.so_dong]);

  // ===== HANDLERS =====
  /**
   * Xử lý submit form tìm kiếm - gọi API với parameters từ form
   */
  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangCauHinhMauHopDongParams) => {
      setFilterParams({
        ma_doi_tac_ql: values.ma_doi_tac_ql || "",
        nv: values.nv || "",
        ma_sp: values.ma_sp || "",
        ten: values.ten || "",
        trang_thai: values.trang_thai || "",
        ngay_ad: values.ngay_ad || undefined,
        trang: 1,
        so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize,
      });
    },
    [setFilterParams, filterParams?.so_dong],
  );

  /**
   * Xử lý thay đổi trang/số dòng trong pagination table
   */
  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      setFilterParams(prev => ({
        ...prev,
        trang: newPage,
        so_dong: newPageSize,
      }));
    },
    [setFilterParams],
  );

  /**
   * Xử lý tìm kiếm trong filter dropdown của table column
   */
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  /**
   * Xử lý reset filter trong dropdown của table column
   */
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  /**
   * Xử lý thay đổi đối tác trong form tìm kiếm - reset nghiệp vụ và sản phẩm
   */
  const handleDoiTacSearchChange = useCallback((value: any) => {
    searchForm.setFieldsValue({
      nv: "",
      ma_sp: ""
    });
  }, [searchForm]);

  /**
   * Xử lý thay đổi nghiệp vụ trong form tìm kiếm - reset sản phẩm
   */
  const handleNghiepVuSearchChange = useCallback((value: any) => {
    searchForm.setFieldsValue({
      ma_sp: ""
    });
  }, [searchForm]);

  /**
   * Xử lý click vào dòng table - lấy chi tiết và mở modal
   */
  const handleRowClick = useCallback(async (record: TableCauHinhMauHopDongColumnDataType) => {
    if (record.key.toString().includes("empty") || loading) return;
    
    try {
      if (!record.bt) return;
      
      const response = await getChiTietCauHinhMauHopDong({bt: parseInt(record.bt)});
      
      if (response && Object.keys(response).length > 0) {
        modalChiTietRef.current?.open(response);
      }
    } catch (error) {
      console.error("Error getting detail:", error);
    }
  }, [loading, getChiTietCauHinhMauHopDong]);

  /**
   * Xử lý click nút "Tạo mới" - mở modal với form trống
   */
  const handleThemMoi = useCallback(() => {
    modalChiTietRef.current?.open();
  }, []);

  /**
   * Callback sau khi save thành công trong modal - refresh danh sách
   */
  const handleAfterSave = useCallback(() => {
    searchCauHinhMauHopDong();
  }, [searchCauHinhMauHopDong]);

  /**
   * Render form input với Col wrapper - dùng chung cho các field trong form tìm kiếm
   */
  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  /**
   * Render header table chứa form tìm kiếm và các nút action
   */
  const renderHeaderTableCauHinhMauHopDong = useCallback(
    () => {
      return (
        <Form
          form={searchForm}
          layout="vertical"
          className="[&_.ant-form-item]:mb-0"
          onFinish={onSearchApi}>
          <Row gutter={16} align="bottom">
            {renderFormInputColumn({...ma_doi_tac_ql, options: dropdownOptions.doiTacQuanLy, onChange: handleDoiTacSearchChange}, 4)}
            {renderFormInputColumn({...nv, options: dropdownOptions.nghiepVu, onChange: handleNghiepVuSearchChange}, 4)}
            {renderFormInputColumn({...ma_sp, options: dropdownOptions.sanPham}, 4)}
            {renderFormInputColumn(ten, 4)}
            {renderFormInputColumn({...trang_thai, options: dropdownOptions.trangThai}, 3)}
            <Col span={2}>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
            <Col span={2}>
              <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </Form>
      );
    },
    [searchForm, onSearchApi, dropdownOptions, loading, handleThemMoi, handleDoiTacSearchChange, handleNghiepVuSearchChange, renderFormInputColumn],
  );

  /**
   * Tạo các props cho column search trong table - filter dropdown và highlight text
   */
  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TableCauHinhMauHopDongColumnDataType> => ({
      filterDropdown:
        dataIndex !== "trang_thai_ten"
          ? (filterDropdownParams) => (
              <TableFilterDropdown
                ref={searchInput}
                title={title}
                dataIndex={dataIndex}
                handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
                handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
                {...filterDropdownParams}
              />
            )
          : undefined,
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      filterDropdownProps: {
        onOpenChange(open) {
          if (open) {
            setTimeout(() => searchInput.current?.select(), 100);
          }
        },
      },
      render: (text, record) => {
        if (dataIndex === "trang_thai_ten") {
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          if (record.key.toString().includes("empty")) return "";
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        return searchedColumn === dataIndex ? (
          <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    }),
    [searchText, searchedColumn, handleSearch, handleReset],
  );

  // ===== MAIN RENDER =====
  return (
    <div className="[&_.ant-space]:w-full">
      <Table<TableCauHinhMauHopDongColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={
          tableColumnConfigs?.map(item => {
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as DataIndex, item.title as string))};
          }) || []
        }
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          onChange: onChangePage,
        }}
        title={renderHeaderTableCauHinhMauHopDong}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietCauHinhMauHopDong 
        ref={modalChiTietRef} 
        onAfterSave={handleAfterSave} 
      />
    </div>
  );
});

CauHinhMauHopDongContent.displayName = "CauHinhMauHopDongContent";
export default CauHinhMauHopDongContent;
