import {ReactQuery} from "@src/@types";

/**
 * Interface định nghĩa các props và methods mà Provider cung cấp cho các component con
 * thông qua Context API để quản lý danh sách khách hàng
 */
export interface IQuanLyKhachHangContextProps {
  // ===== STATE DATA - DỮ LIỆU TRẠNG THÁI =====
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>; // Danh sách khách hàng
  tongSoDong: number; // Tổng số dòng dữ liệu để phân trang
  loading: boolean; // Trạng thái loading khi gọi API

  // ===== FILTER PARAMETERS - THAM SỐ LỌC VÀ PHÂN TRANG =====
  filterParams: ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang; // Tham số tìm kiếm và phân trang

  // ===== API METHODS - CÁC PHƯƠNG THỨC GỌI API =====
  /**
   * Tìm kiếm phân trang danh sách khách hàng
   * @param params - Tham số tìm kiếm và phân trang
   */
  timKiemPhanTrangKhachHang: (params?: ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang) => Promise<void>;

  /**
   * Lấy chi tiết thông tin khách hàng
   * @param params - Tham số để lấy chi tiết khách hàng
   */
  layChiTietKhachHang: (params: {ma_kh: string; ma_doi_tac_ql?: string}) => Promise<CommonExecute.Execute.IKhachHang | null>;

  // ===== STATE MANAGEMENT - QUẢN LÝ TRẠNG THÁI =====
  /**
   * Cập nhật tham số filter và phân trang
   * @param params - Tham số filter mới
   */
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang>>;
}
