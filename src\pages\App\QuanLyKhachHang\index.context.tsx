import {createContext, useContext} from "react";

import {IQuanLyKhachHangContextProps} from "./index.model";

/**
 * Context cho phép các component con truy cập state và functions mà không cần prop drilling
 * Khởi tạo giá trị mặc định cho context
 */
export const QuanLyKhachHangContext = createContext<IQuanLyKhachHangContextProps>({
  // ===== STATE DATA - DỮ LIỆU TRẠNG THÁI =====
  danhSachKhachHang: [], // Danh sách khách hàng rỗng
  tongSoDong: 0, // Tổng số dòng = 0
  loading: false, // Không loading
  filterParams: {}, // Tham số filter rỗng
  
  // ===== API METHODS - CÁC PHƯƠNG THỨC GỌI API =====
  timKiemPhanTrangKhachHang: async () => Promise.resolve(), // Hàm tìm kiếm phân trang mặc định
  layChiTietKhachHang: async () => Promise.resolve({} as CommonExecute.Execute.IKhachHang), // Hàm lấy chi tiết mặc định
  
  // ===== STATE MANAGEMENT - QUẢN LÝ TRẠNG THÁI =====
  setFilterParams: () => {}, // Hàm set filter params mặc định
});

/**
 * Custom hook để sử dụng QuanLyKhachHangContext
 * @returns IQuanLyKhachHangContextProps
 */
export const useQuanLyKhachHangContext = () => useContext(QuanLyKhachHangContext);
