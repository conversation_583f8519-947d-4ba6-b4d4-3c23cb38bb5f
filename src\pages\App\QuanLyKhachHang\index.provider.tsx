import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {message} from "antd";
import {isEqual} from "lodash";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";

import {QuanLyKhachHangContext} from "./index.context";
import {IQuanLyKhachHangContextProps} from "./index.model";
import {defaultValueFormTimKiemPhanTrang, PAGE_SIZE} from "./index.configs";

/**
 * Provider component cung cấp state và methods cho các component con
 * thông qua Context API
 */
const QuanLyKhachHangProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  // ===== STATE MANAGEMENT - QUẢN LÝ TRẠNG THÁI =====
  const [danhSachKhachHang, setDanhSachKhachHang] = useState<CommonExecute.Execute.IKhachHang[]>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang>(defaultValueFormTimKiemPhanTrang);

  // ===== API METHODS - CÁC PHƯƠNG THỨC GỌI API =====

  /**
   * Tìm kiếm phân trang danh sách khách hàng
   * Sử dụng API action code: QESH7W1GNQNNWJ1
   */
  const timKiemPhanTrangKhachHang = useCallback(
    async (params?: ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const searchParams = params || filterParams;

        // Chuẩn bị tham số cho API
        const apiParams = {
          ma_doi_tac_ql: searchParams.ma_doi_tac_ql || "",
          ma_chi_nhanh_ql: searchParams.ma_chi_nhanh_ql || "",
          loai_kh: searchParams.loai_kh || "",
          ma: searchParams.ma || "",
          ten: searchParams.ten || "",
          dthoai: searchParams.dthoai || "",
          cmt: searchParams.cmt || "",
          mst: searchParams.mst || "",
          nd_tim: searchParams.nd_tim || "",
          trang: searchParams.trang || 1,
          so_dong: searchParams.so_dong || PAGE_SIZE,
        };

        // Gọi API
        const response = await mutateUseCommonExecute.mutateAsync({
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_KHACH_HANG, // Action code cho tìm kiếm khách hàng
          ...apiParams,
        });

        if (response?.data) {
          const {data, tong_so_dong} = response.data;

          // Cập nhật state
          setDanhSachKhachHang(data || []);
          setTongSoDong(tong_so_dong || 0);

          // Cập nhật filter params nếu có params mới
          if (params && !isEqual(params, filterParams)) {
            setFilterParams(params);
          }
        } else {
          console.warn("⚠️ [QuanLyKhachHang] Response không có dữ liệu");
          setDanhSachKhachHang([]);
          setTongSoDong(0);
        }
      } catch (error) {
        console.error("❌ [QuanLyKhachHang] Lỗi khi tìm kiếm khách hàng:", error);
        message.error("Có lỗi xảy ra khi tìm kiếm khách hàng");
        setDanhSachKhachHang([]);
        setTongSoDong(0);
      }
    },
    [mutateUseCommonExecute, filterParams],
  );

  /**
   * Lấy chi tiết thông tin khách hàng
   * TODO: Implement khi có API action code cho chi tiết khách hàng
   */
  const layChiTietKhachHang = useCallback(
    async (params: {ma_kh: string; ma_doi_tac_ql?: string}): Promise<CommonExecute.Execute.IKhachHang | null> => {
      try {
        // TODO: Implement API call khi có action code
        // const response = await mutateUseCommonExecute.mutateAsync({
        //   action_code: ACTION_CODE.LAY_CHI_TIET_KHACH_HANG,
        //   params: {
        //     b_ma_kh: params.ma_kh,
        //     b_ma_doi_tac_ql: params.ma_doi_tac_ql || "",
        //   },
        // });

        // Tạm thời return null
        return null;
      } catch (error) {
        console.error("❌ [QuanLyKhachHang] Lỗi khi lấy chi tiết khách hàng:", error);
        message.error("Có lỗi xảy ra khi lấy chi tiết khách hàng");
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // ===== EFFECTS - CÁC HIỆU ỨNG =====

  /**
   * Load dữ liệu ban đầu khi component mount
   */
  useEffect(() => {
    timKiemPhanTrangKhachHang();
  }, []);

  // ===== CONTEXT VALUE - GIÁ TRỊ CONTEXT =====

  /**
   * Tạo context value với useMemo để tối ưu performance
   */
  const contextValue = useMemo<IQuanLyKhachHangContextProps>(
    () => ({
      // State data
      danhSachKhachHang,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,

      // API methods
      timKiemPhanTrangKhachHang,
      layChiTietKhachHang,

      // State management
      setFilterParams,
    }),
    [danhSachKhachHang, tongSoDong, mutateUseCommonExecute.isLoading, filterParams, timKiemPhanTrangKhachHang, layChiTietKhachHang, setFilterParams],
  );

  return <QuanLyKhachHangContext.Provider value={contextValue}>{children}</QuanLyKhachHangContext.Provider>;
};

export default QuanLyKhachHangProvider;
