/**
 * Chức năng: <PERSON><PERSON><PERSON> nghĩa cấu hình table, form, interfaces và helper functions cho dropdown options
 */

import React from "react";
import {TableColumnsType, TableProps} from "antd";
import moment from "moment";

import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";

/**
 * Interface cho form tìm kiếm
 */
export interface ICauHinhMauHopDongSearchForm {
  ma_doi_tac_ql?: string;
  ma_sp?: string;
  ten?: string;
  trang_thai?: string;
}

/**
 * Interface cho form trong modal
 */
export interface ICauHinhMauHopDongModalForm {
  ma_doi_tac_ql?: string;
  nv?: string;
  ma_sp?: string;
  ten?: string;
  ngay_ad?: moment.Moment;
  trang_thai?: string;
  id_file?: number;
  url_file?: string; // Giữ lại để hiển thị, nhưng không submit
}

/**
 * Interface cho data type của table columns
 */
export interface TableCauHinhMauHopDongColumnDataType {
  key: string;
  sott?: number;
  bt?: string; 
  ma_doi_tac_ql?: string; 
  ten_doi_tac_ql?: string;
  nv?: string; 
  ten_nv?: string;
  ma_sp?: string; 
  ten_sp?: string;
  ten?: string; 
  ngay_ad?: number;
  ngay_ad_date?: string;
  id_file?: number;
  url_file?: string;
  trang_thai?: string; 
  ngay_tao?: string;
  nguoi_tao?: string; 
  ngay_cap_nhat?: string; 
  nguoi_cap_nhat?: string; 
  trang_thai_ten?: string; 
}

export type TableCauHinhMauHopDongColumnDataIndex = keyof TableCauHinhMauHopDongColumnDataType;

//ĐỊNH NGHĨA CÁC FIELD TRONG FORM TÌM KIẾM
export interface IFormTimKiemCauHinhMauHopDongFieldsConfig {
  ma_doi_tac_ql: IFormInput; 
  nv: IFormInput;
  ma_sp: IFormInput; 
  ten: IFormInput;
  trang_thai: IFormInput; 
}

/**
 * Object này định nghĩa các field tìm kiếm hiển thị ở phần header của table
 */
export const FormTimKiemCauHinhMauHopDong: IFormTimKiemCauHinhMauHopDongFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select", 
    name: "ma_doi_tac_ql", 
    label: "Đối tác quản lý", 
    placeholder: "Chọn đối tác quản lý", 
    className: "!mb-0", 
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
    className: "!mb-0",
  },
  ma_sp: {
    component: "select",
    name: "ma_sp",
    label: "Sản phẩm",
    placeholder: "Chọn sản phẩm",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên mẫu hợp đồng",
    placeholder: "Nhập tên mẫu hợp đồng",
    className: "!mb-0",
  },
  trang_thai: {
    component: "select", 
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

// DROPDOWN OPTIONS - CENTRALIZED 

/**
 * Options tĩnh cho trạng thái - dùng cho search form
 */
export const TRANG_THAI_SEARCH_OPTIONS = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

/**
 * Options tĩnh cho trạng thái - dùng cho modal
 */
export const TRANG_THAI_MODAL_OPTIONS = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngưng sử dụng"},
];

/**
 * Base option cho dropdown "Tất cả"
 */
export const ALL_OPTION = {ma: "", ten: "Tất cả"};

// HELPER FUNCTIONS CHO DROPDOWN OPTIONS 

/**
 * Tạo danh sách đối tác từ listSanPham
 */
export const createDoiTacOptions = (listSanPham: any[], includeAll = true) => {
  if (!listSanPham || !Array.isArray(listSanPham)) {
    return includeAll ? [ALL_OPTION] : [];
  }
  
  const uniquePartners = Array.from(
    new Map(
      listSanPham
        .filter(item => item && item.ma_doi_tac_ql)
        .map(item => [
          item.ma_doi_tac_ql!, 
          {
            ma: item.ma_doi_tac_ql!, 
            ten: item.doi_tac_ql_ten_tat || item.ma_doi_tac_ql!
          }
        ])
    ).values()
  );
  
  return includeAll ? [ALL_OPTION, ...uniquePartners] : uniquePartners;
};

/**
 * Tạo danh sách nghiệp vụ từ listSanPham (có thể filter theo đối tác)
 */
export const createNghiepVuOptions = (listSanPham: any[], selectedDoiTac?: string, includeAll = true) => {
  if (!listSanPham || !Array.isArray(listSanPham)) {
    return includeAll ? [ALL_OPTION] : [];
  }
  let nghiepVuData = listSanPham.filter(item => item && item.nv);
  // Filter theo đối tác nếu có
  if (selectedDoiTac && selectedDoiTac !== "") {
    nghiepVuData = nghiepVuData.filter(item => item.ma_doi_tac_ql === selectedDoiTac);
  }
  const uniqueNghiepVu = Array.from(
    new Map(
      nghiepVuData.map(item => [item.nv!, {
        ma: item.nv!, 
        ten: item.ten_nv || item.nv!
      }])
    ).values()
  );
  return includeAll ? [ALL_OPTION, ...uniqueNghiepVu] : uniqueNghiepVu;
};

/**
 * Tạo danh sách sản phẩm từ listSanPham (có thể filter theo đối tác + nghiệp vụ)
 */
export const createSanPhamOptions = (listSanPham: any[], selectedDoiTac?: string, selectedNghiepVu?: string, includeAll = true) => {
  if (!listSanPham || !Array.isArray(listSanPham)) {
    return includeAll ? [ALL_OPTION] : [];
  }
  
  let sanPhamData = listSanPham.filter(item => item && item.ma && item.ten);
  
  // Filter theo đối tác nếu có
  if (selectedDoiTac && selectedDoiTac !== "") {
    sanPhamData = sanPhamData.filter(item => item.ma_doi_tac_ql === selectedDoiTac);
  }
  
  // Filter theo nghiệp vụ nếu có
  if (selectedNghiepVu && selectedNghiepVu !== "") {
    sanPhamData = sanPhamData.filter(item => item.nv === selectedNghiepVu);
  }
  
  // Filter chỉ lấy sản phẩm đang hoạt động cho modal
  if (!includeAll) {
    sanPhamData = sanPhamData.filter(item => item.trang_thai !== "K");
  }
  
  const uniqueSanPham = sanPhamData.map(item => ({
    ma: item.ma!, 
    ten: `${item.ma!} - ${item.ten!}`
  }));
  
  return includeAll ? [ALL_OPTION, ...uniqueSanPham] : uniqueSanPham;
};

/**
 * Tạo tất cả dropdown options cho search form
 */
export const createSearchDropdownOptions = (listSanPham: any[], searchDoiTac?: string, searchNghiepVu?: string) => {
  return {
    doiTacQuanLy: createDoiTacOptions(listSanPham, true),
    nghiepVu: createNghiepVuOptions(listSanPham, searchDoiTac, true),
    sanPham: createSanPhamOptions(listSanPham, searchDoiTac, searchNghiepVu, true),
    trangThai: TRANG_THAI_SEARCH_OPTIONS
  };
};

/**
 * Tạo tất cả dropdown options cho modal
 */
export const createModalDropdownOptions = (listSanPham: any[], selectedDoiTac?: string, selectedNghiepVu?: string) => {
  return {
    doiTac: createDoiTacOptions(listSanPham, false),
    nghiepVu: createNghiepVuOptions(listSanPham, selectedDoiTac, false),
    sanPham: createSanPhamOptions(listSanPham, selectedDoiTac, selectedNghiepVu, false),
    trangThai: TRANG_THAI_MODAL_OPTIONS
  };
};

/**
 * Cấu hình cột cho bảng
 */
export const tableColumnConfigs: TableColumnsType<CommonExecute.Execute.ICauHinhMauHopDong> = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    width: colWidthByKey.sott,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Đối tác",
    dataIndex: "ma_doi_tac_ql",
    key: "ma_doi_tac_ql",
    width: 80,
    ...defaultTableColumnsProps,
  },
  {
    title: "Nghiệp vụ",
    dataIndex: "nv_ten",
    key: "nv_ten",
    width: 170,
    ...defaultTableColumnsProps,
  },
  {
    title: "Sản phẩm",
    dataIndex: "ten_sp",
    key: "ten_sp",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên mẫu hợp đồng",
    dataIndex: "ten",
    key: "ten",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
    width: 120,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "File mẫu",
    dataIndex: "id_file",
    key: "id_file",
    width: 150,
    align: "center",
    ...defaultTableColumnsProps,
    render: (id_file: number, record: any) => {
      if (!id_file) return '-';
      
      const displayText = record.url_file 
        ? `ID: ${id_file}` 
        : `File ID: ${id_file}`;
      
      return (
        <span title={record.url_file || `File ID: ${id_file}`}>
          {displayText}
        </span>
      );
    },
  },
  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    width: colWidthByKey.ngay_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    width: colWidthByKey.nguoi_tao,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    width: colWidthByKey.ngay_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    width: colWidthByKey.nguoi_cap_nhat,
    align: "center",
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    width: 100,
    align: "center",
    ...defaultTableColumnsProps,
  }
];

/**
 * Default props cho table
 */
export const defaultTableProps: TableProps<CommonExecute.Execute.ICauHinhMauHopDong> = {
  bordered: true,
  size: "middle",
  scroll: {x: "max-content"},
  locale: {emptyText: "Không có dữ liệu"},
};
