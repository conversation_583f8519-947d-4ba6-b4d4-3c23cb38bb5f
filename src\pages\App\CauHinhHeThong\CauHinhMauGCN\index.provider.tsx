/**
 * Chức năng: Cung cấp state management, API calls và business logic cho toàn bộ module
 */
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState, useRef} from "react";
import {message} from "antd";
import {isEqual} from "lodash";

import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {useCommonExecute} from "@src/services/react-queries";
import {defaultPaginationTableProps} from "@src/hooks";

import CauHinhMauGCNContext from "./index.context";
import {ICauHinhMauGCNProvider} from "./index.model";

/**
 * Component này wrap các component con và cung cấp context với state và methods
 */
const CauHinhMauGCNProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;

  // ===== HOOKS VÀ STATE MANAGEMENT =====
  const mutateUseCommonExecute = useCommonExecute();
  
  // ===== FILTER PARAMETERS STATE - SỬ DỤNG useRef ĐỂ TRÁNH DEPENDENCY ISSUES =====
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangCauHinhMauGCNParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    ma_sp: "",
    ten: "", 
    nv: "",
    trang_thai: "", 
    ngay_ad: undefined,
    trang: 1,
    so_dong: defaultPaginationTableProps.defaultPageSize, 
  });

  // SỬ DỤNG useRef ĐỂ LƯU filterParams TRÁNH DEPENDENCY CYCLE
  const filterParamsRef = useRef(filterParams);
  filterParamsRef.current = filterParams;

  // ===== STATE MANAGEMENT =====
  const [loading, setLoading] = useState<boolean>(false);
  const [danhSachCauHinhMauGCN, setDanhSachCauHinhMauGCN] = useState<Array<any>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [initialized, setInitialized] = useState<boolean>(false);

  // ===== CHỈ CẦN 1 DANH SÁCH SẢN PHẨM - CHỨA TẤT CẢ THÔNG TIN =====
  const [listSanPham, setListSanPham] = useState<Array<CommonExecute.Execute.ISanPham>>([]);

  // ===== API LẤY TẤT CẢ SẢN PHẨM (SỬ DỤNG API CÓ SẴN) =====
  const getListSanPham = useCallback(async () => {
    try {
      console.log("[DEBUG PROVIDER] Loading all products...");
      
      // Sử dụng API có sẵn với params rỗng để lấy tất cả
      const response = await mutateUseCommonExecute.mutateAsync({
        ma_doi_tac_ql: "",
        ma: "",
        ten: "",
        nv: "",
        trang_thai: "D",
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_SAN_PHAM,
      } as any);
      
      console.log("[DEBUG PROVIDER] Raw products from API:", response?.data);
      
      const responseData = response.data as any;
      if (responseData?.data && Array.isArray(responseData.data)) {
        // Lọc chỉ lấy sản phẩm active
        const activeProducts = responseData.data.filter((item: any) => 
          item && item.trang_thai !== "K"
        );
        setListSanPham(activeProducts);
        
        console.log("[DEBUG PROVIDER] Products loaded:", {
          total: responseData.data.length,
          active: activeProducts.length
        });
      } else {
        setListSanPham([]);
      }
      
    } catch (error) {
      console.error("[DEBUG PROVIDER] Error loading products:", error);
      setListSanPham([]);
    }
  }, [mutateUseCommonExecute]);

  /**
   * Tìm kiếm cấu hình mẫu giấy chứng nhận - STABLE FUNCTION
   */
  const searchCauHinhMauGCN = useCallback(async () => {
    try {
      const currentParams = filterParamsRef.current;
      const requestPayload = {
        ...currentParams,
        actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_CAU_HINH_MAU_GCN,
      };
      
      console.log("[DEBUG] searchCauHinhMauGCN request payload:", requestPayload);
      
      const response = await mutateUseCommonExecute.mutateAsync(requestPayload as any);
      
      const responseData = response.data;
      if (responseData?.data && Array.isArray(responseData.data)) {
        setDanhSachCauHinhMauGCN(responseData.data); 
        setTongSoDong(responseData.tong_so_dong || responseData.data.length);
      } else {
        setDanhSachCauHinhMauGCN([]);
        setTongSoDong(0);
      }
    } catch (error) {
      console.log("searchCauHinhMauGCN error:", error);
      message.error("Không thể tải danh sách cấu hình mẫu giấy chứng nhận");
    }
  }, [mutateUseCommonExecute]);

  // Sử dụng ref để tránh dependency cycle
  const searchCauHinhMauGCNRef = useRef(searchCauHinhMauGCN);
  searchCauHinhMauGCNRef.current = searchCauHinhMauGCN;

  /**
   * Lấy chi tiết cấu hình mẫu giấy chứng nhận
   */
  const getChiTietCauHinhMauGCN = useCallback(
    async (data: {bt: number}) => {
      try {
        console.log("[DEBUG] getChiTietCauHinhMauGCN request:", data);
        
        const response = await mutateUseCommonExecute.mutateAsync({
          bt: data.bt, 
          actionCode: ACTION_CODE.CHI_TIET_CAU_HINH_MAU_GCN,
        } as any);
        
        console.log("[DEBUG] getChiTietCauHinhMauGCN response:", response);
        
        const responseData = response.data as any;
        if (responseData?.lke && Array.isArray(responseData.lke) && responseData.lke.length > 0) {
          const detailData = responseData.lke[0] as CommonExecute.Execute.ICauHinhMauGCN;
          console.log("[DEBUG] getChiTietCauHinhMauGCN detail data:", detailData);
          return detailData;
        }
        
        console.log("[DEBUG] getChiTietCauHinhMauGCN: No data found");
        return {} as CommonExecute.Execute.ICauHinhMauGCN;
      } catch (error) {
        console.log("getChiTietCauHinhMauGCN error:", error);
        message.error("Không thể tải chi tiết cấu hình mẫu giấy chứng nhận");
        return {} as CommonExecute.Execute.ICauHinhMauGCN;
      }
    },
    [mutateUseCommonExecute],
  );

  /**
   * Cập nhật cấu hình mẫu giấy chứng nhận
   */
  const capNhatChiTietCauHinhMauGCN = useCallback(
    async (data: ReactQuery.ICapNhatCauHinhMauGCNParams) => {
      try {
        console.log("[DEBUG PROVIDER] capNhatChiTietCauHinhMauGCN input data:", data);
        
        const requestPayload = {
          ...data, 
          actionCode: ACTION_CODE.UPDATE_CAU_HINH_MAU_GCN,
        };
        
        console.log("[DEBUG PROVIDER] API request payload:", requestPayload);
        console.log("[DEBUG PROVIDER] Action code:", ACTION_CODE.UPDATE_CAU_HINH_MAU_GCN);
        
        const response = await mutateUseCommonExecute.mutateAsync(requestPayload as any);
        
        console.log("[DEBUG PROVIDER] API response:", response);
        message.success(data.bt ? "Cập nhật thành công" : "Thêm mới thành công");
        return response.data;
      } catch (error: any) {
        console.log("capNhatChiTietCauHinhMauGCN error:", error);
        console.log("Error details:", {
          status: error?.response?.status,
          data: error?.response?.data,
          message: error?.message
        });
        message.error(data.bt ? "Có lỗi khi cập nhật" : "Có lỗi khi thêm mới");
        return {};
      }
    },
    [mutateUseCommonExecute],
  );

  // ===== KHỞI TẠO DATA =====
  useEffect(() => {
    if (!initialized) {
      console.log("[DEBUG] Initializing data...");
      getListSanPham();
      searchCauHinhMauGCNRef.current();
      setInitialized(true);
    }
  }, [initialized]); 

  // ===== EFFECT: TÌM KIẾM KHI filterParams THAY ĐỔI =====
  useEffect(() => {
    if (initialized) {
      console.log("[DEBUG] filterParams changed, calling searchCauHinhMauGCN");
      searchCauHinhMauGCNRef.current();
    }
  }, [filterParams, initialized]);

  // ===== CONTEXT VALUE =====
  const contextValue: ICauHinhMauGCNProvider = useMemo(() => ({
    // State values
    listSanPham,
    danhSachCauHinhMauGCN,
    tongSoDong,
    loading: mutateUseCommonExecute.isLoading,
    filterParams,
    // API functions
    searchCauHinhMauGCN,
    getChiTietCauHinhMauGCN,
    capNhatChiTietCauHinhMauGCN,
    getListSanPham,
    // State setters
    setFilterParams,
  }), [
    listSanPham,
    danhSachCauHinhMauGCN,
    tongSoDong,
    mutateUseCommonExecute.isLoading,
    filterParams,
    searchCauHinhMauGCN,
    getChiTietCauHinhMauGCN,
    capNhatChiTietCauHinhMauGCN,
    getListSanPham,
    setFilterParams,
  ]);

  return <CauHinhMauGCNContext.Provider value={contextValue}>{children}</CauHinhMauGCNContext.Provider>;
};

export default CauHinhMauGCNProvider;
