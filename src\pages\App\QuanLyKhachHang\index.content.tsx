import {ClearOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE} from "@src/constants";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty, useChiNhanh, useDoiTac} from "@src/hooks";
import {Col, Form, Input, InputRef, Row, Select, Table, TableColumnType, Tag, Tooltip} from "antd";
import {memo, useCallback, useEffect, useMemo, useRef, useState} from "react";

import {
  FormTimKiemKhachHang,
  LOAI_KHACH_HANG_OPTIONS,
  PAGE_SIZE,
  tableKhachHangColumn,
  TableKhachHangColumnDataIndex,
  TableKhachHangColumnDataType,
  TRANG_THAI_KHACH_HANG_OPTIONS,
} from "./index.configs";
import {useQuanLyKhachHangContext} from "./index.context";
import "./index.default.scss";

// Destructure form fields để sử dụng
const {ma_doi_tac_ql, ma_chi_nhanh_ql, loai_kh, ma, ten, dthoai, cmt, mst, nd_tim} = FormTimKiemKhachHang;

/**
 * Component chính hiển thị giao diện quản lý khách hàng
 */
const QuanLyKhachHangContent: React.FC = memo(() => {
  const {danhSachKhachHang, loading, tongSoDong, timKiemPhanTrangKhachHang, filterParams, setFilterParams} = useQuanLyKhachHangContext();

  // ===== HOOKS =====
  const {listDoiTac} = useDoiTac(); // Hook lấy danh sách đối tác
  const {listChiNhanh} = useChiNhanh(); // Hook lấy danh sách chi nhánh
  const [formTimKiemKhachHang] = Form.useForm(); // Form instance
  const refSearchInputTable = useRef<InputRef>(null); // Ref cho input search trong table

  // ===== FORM WATCHING =====
  const watchMaDoiTac = Form.useWatch("ma_doi_tac_ql", formTimKiemKhachHang); // Watch giá trị đối tác được chọn

  // ===== STATE =====
  const [searchTextTable, setSearchTextTable] = useState<string>(""); // Text search trong table
  const [searchedColumn, setSearchedColumn] = useState<TableKhachHangColumnDataIndex | "">(""); // Column đang được search
  const [page, setPage] = useState(1); // Trang hiện tại
  const isFirstRender = useRef(true); // Ref để track lần render đầu tiên

  // ===== COMPUTED VALUES =====
  /**
   * Lọc danh sách chi nhánh theo đối tác được chọn
   * Chỉ hiển thị các chi nhánh thuộc đối tác đã chọn
   */
  const listChiNhanhFilterTheoDoiTac = useMemo(() => {
    if (!watchMaDoiTac) {
      return listChiNhanh; // Nếu chưa chọn đối tác, hiển thị tất cả chi nhánh
    }
    const filtered = listChiNhanh.filter(item => item.ma_doi_tac === watchMaDoiTac);
    return filtered;
  }, [listChiNhanh, watchMaDoiTac]);

  // ===== SIDE EFFECTS =====
  /**
   * Xóa chi nhánh đã chọn khi thay đổi đối tác
   * Đảm bảo chi nhánh được chọn luôn thuộc đối tác hiện tại
   */
  useEffect(() => {
    // Bỏ qua lần đầu tiên để không xóa default values
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Chỉ validate khi dữ liệu đã load (listChiNhanh có dữ liệu)
    if (listChiNhanh.length === 0) {
      return;
    }

    const currentChiNhanh = formTimKiemKhachHang.getFieldValue("ma_chi_nhanh_ql");

    if (currentChiNhanh && watchMaDoiTac) {
      // Kiểm tra xem chi nhánh hiện tại có thuộc đối tác mới không
      const isChiNhanhBelongToDoiTac = listChiNhanhFilterTheoDoiTac.some(item => item.value === currentChiNhanh || item.ma === currentChiNhanh);

      // Nếu chi nhánh không thuộc đối tác mới, xóa selection
      if (!isChiNhanhBelongToDoiTac) {
        formTimKiemKhachHang.setFieldValue("ma_chi_nhanh_ql", undefined);
      }
    }
  }, [watchMaDoiTac, listChiNhanhFilterTheoDoiTac, formTimKiemKhachHang, listChiNhanh.length]);

  // ===== TABLE DATA PROCESSING =====

  /**
   * Xử lý dữ liệu cho table, thêm STT và fill empty rows
   */
  const dataTableKhachHang = (() => {
    try {
      const tableData = danhSachKhachHang.map((item: any, index: number) => ({
        ...item,
        sott: ((filterParams.trang || 1) - 1) * PAGE_SIZE + index + 1, // STT theo trang
        key: item.ma || index.toString(), // Key unique cho table
      }));

      // Fill empty rows để table có đủ số dòng
      const arrEmptyRow: Array<TableKhachHangColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.error("❌ [QuanLyKhachHang] Lỗi xử lý dữ liệu table:", error);
      return [];
    }
  })();

  // ===== EVENT HANDLERS =====

  /**
   * Xử lý submit form tìm kiếm
   */
  const onTimKiemKhachHang = useCallback(
    (values: ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang) => {
      // Chuẩn bị tham số tìm kiếm
      const searchParams = {
        ...values,
        trang: 1, // Reset về trang 1 khi tìm kiếm mới
        so_dong: PAGE_SIZE,
      };

      // Cập nhật filter params và gọi API
      setFilterParams(searchParams);
      setPage(1);
      timKiemPhanTrangKhachHang(searchParams);
    },
    [timKiemPhanTrangKhachHang, setFilterParams],
  );

  /**
   * Xử lý thay đổi trang
   */
  const onChangePage = useCallback(
    (page: number, pageSize: number) => {
      const newParams = {
        ...filterParams,
        trang: page,
        so_dong: pageSize,
      };

      setPage(page);
      setFilterParams(newParams);
      timKiemPhanTrangKhachHang(newParams);
    },
    [filterParams, timKiemPhanTrangKhachHang, setFilterParams],
  );

  // ===== TABLE SEARCH FUNCTIONS =====

  // Xử lý search trong table

  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: TableKhachHangColumnDataIndex) => {
    confirm();
    setSearchTextTable(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  /**
   * Xử lý reset search trong table
   */
  const handleReset = (clearFilters: () => void, confirm: () => void, dataIndex: TableKhachHangColumnDataIndex) => {
    clearFilters();
    confirm();
    setSearchTextTable("");
    setSearchedColumn(dataIndex);
  };

  /**
   * Tạo props cho column search
   */
  const getColumnSearchProps = (dataIndex: TableKhachHangColumnDataIndex, title: string): TableColumnType<TableKhachHangColumnDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={refSearchInputTable}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]!.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => refSearchInputTable.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? TRANG_THAI_KHACH_HANG_OPTIONS : undefined,
    render: (text, record) => {
      // Render cho trạng thái
      if (dataIndex === "trang_thai_ten") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key?.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }

      // Render với highlight search text
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });

  // ===== RENDER HELPER FUNCTIONS =====

  /**
   * Render form input column
   */
  const renderFormInputColumn = (props: any, span: number = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  /**
   * Render header table với form tìm kiếm
   */
  const renderHeaderTable = () => {
    return (
      <>
        <Form form={formTimKiemKhachHang} initialValues={{ma_doi_tac_ql: "ESCS", ma_chi_nhanh_ql: "TCT"}} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onTimKiemKhachHang}>
          <Row gutter={16} align="bottom">
            {renderFormInputColumn({...ma_doi_tac_ql, options: listDoiTac, fieldNames: {label: "ten_tat", value: "ma"}})}
            {renderFormInputColumn({...ma_chi_nhanh_ql, options: listChiNhanhFilterTheoDoiTac, fieldNames: {label: "ten_tat", value: "ma"}})}
            {renderFormInputColumn({...ma})}
            {renderFormInputColumn({...ten})}
            {renderFormInputColumn({...loai_kh, options: LOAI_KHACH_HANG_OPTIONS})}
            {renderFormInputColumn({...dthoai})}
            {renderFormInputColumn({...cmt})}
            {renderFormInputColumn({...mst})}
            {renderFormInputColumn({...nd_tim})}
            <Col>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
                Tìm kiếm
              </Button>
            </Col>
          </Row>
        </Form>
      </>
    );
  };

  // ===== MAIN RENDER =====

  return (
    <div id="QUAN_LY_KHACH_HANG" className="[&_.ant-space]:w-full">
      <Table<TableKhachHangColumnDataType>
        {...defaultTableProps}
        loading={loading}
        dataSource={dataTableKhachHang}
        columns={tableKhachHangColumn?.map(item => ({
          ...item,
          ...(item.key && typeof item.title === "string" && item.key !== "sott" ? getColumnSearchProps(item.key as TableKhachHangColumnDataIndex, item.title) : {}),
        }))}
        pagination={{
          ...defaultPaginationTableProps,
          total: tongSoDong,
          current: page,
          pageSize: PAGE_SIZE,
          onChange: onChangePage,
        }}
        title={renderHeaderTable}
      />
    </div>
  );
});

QuanLyKhachHangContent.displayName = "QuanLyKhachHangContent";

export default QuanLyKhachHangContent;
