import {Tabs} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle} from "react";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {RenderTabCauHinhDongBaoHiem} from "./RenderTab_CauHinhDongBaoHiem";
import {RenderTabCauHinhTaiBaoHiem} from "./RenderTab_CauHinhTaiBaoHiem";
import {RenderTabThongTinThanhToan} from "./RenderTab_ThongTinThanhToan";

const ThongTinCauHinhBaoHiemXeStepComponent = forwardRef<any, {tableHeight?: number; disabled?: boolean; pageSize?: number}>(({tableHeight, disabled, pageSize = 20}, ref) => {
  useImperativeHandle(ref, () => ({
    resetForm: () => {},
  }));

  const {loading, layDanhSachCauHinhDongCuaHopDongBaoHiemXe, layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe, chiTietHopDongBaoHiemXe} = useBaoHiemXeCoGioiContext();

  useEffect(() => {
    layDanhSachCauHinhDongCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe?.so_id});
    layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe?.so_id});
  }, [chiTietHopDongBaoHiemXe]);

  // RENDER
  return (
    <Tabs
      className="mt-3 [&_.ant-tabs-nav]:mb-0"
      animated={false}
      size="small"
      // tabPosition={"left"}
      defaultActiveKey="1"
      // type="card"
      items={[
        {
          key: "1",
          label: "Thông tin thanh toán",
          children: <RenderTabThongTinThanhToan pageSize={pageSize} disabled={disabled} />,
        },
        {
          key: "2",
          label: "Thông tin đồng bảo hiểm",
          children: <RenderTabCauHinhDongBaoHiem pageSize={pageSize} disabled={disabled} />,
        },
        {
          key: "3",
          label: "Thông tin tái bảo hiểm",
          children: <RenderTabCauHinhTaiBaoHiem pageSize={pageSize} disabled={disabled} />,
        },
      ]}
      // onChange={onChange}
    />
  );
});

ThongTinCauHinhBaoHiemXeStepComponent.displayName = "ThongTinCauHinhBaoHiemXeStepComponent";
export const ThongTinCauHinhBaoHiemXeStep = memo(ThongTinCauHinhBaoHiemXeStepComponent, isEqual);
