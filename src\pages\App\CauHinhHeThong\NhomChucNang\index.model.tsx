import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface NhomChucNangContextProps {
  danhSachNhomChucNang: Array<CommonExecute.Execute.IDanhSachNhomChucNangPhanTrang>;
  loading: boolean;
  onUpdateNhomChucNang: (item: ReactQuery.IUpdateNhomChucNangParams) => Promise<number | null | undefined>;
  layDanhSachNhomChucNangPhanTrang: (params: ReactQuery.ILayDanhSachNhomChucNangPhanTrangParams) => void;
  tongSoDong: number;
  layChiTietNhomChucNang: (params: ReactQuery.IChiTietNhomChucNangParams) => Promise<CommonExecute.Execute.IChiTietNhomChucNang | null>;
  defaultFormValue: object;
  onSyncChucNang: () => void;
  onDeleteChucNangTheoNhom: (item: ReactQuery.IDeleteChucNangTheoNhomParams) => Promise<number | null | undefined>;
  chiTietNhomChucNang: CommonExecute.Execute.IChiTietNhomChucNang;
  setChiTietNhomChucNang: React.Dispatch<React.SetStateAction<CommonExecute.Execute.IChiTietNhomChucNang>>;
}
